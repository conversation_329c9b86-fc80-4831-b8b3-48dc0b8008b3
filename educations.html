<!DOCTYPE html>
<html><head>
<meta charset="utf-8"/>
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
<link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Spline+Sans%3Awght%40400%3B500%3B700" onload="this.rel='stylesheet'" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet"/>
<title>educations</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style type="text/tailwindcss">
      @keyframes glow {
        0%, 100% {
          box-shadow: 0 0 5px theme('colors.cyan.400'), 0 0 10px theme('colors.cyan.400'), 0 0 15px theme('colors.cyan.400');
        }
        50% {
          box-shadow: 0 0 10px theme('colors.purple.500'), 0 0 20px theme('colors.purple.500'), 0 0 30px theme('colors.purple.500');
        }
      }
      .timeline-item-icon {
        animation: glow 3s infinite ease-in-out;
      }
    </style>
</head>
<body class="bg-gray-900 text-gray-200" style='font-family: "Spline Sans", "Noto Sans", sans-serif;'>
<div class="relative flex size-full min-h-screen flex-col group/design-root overflow-x-hidden">
<div class="layout-container flex h-full grow flex-col">
<header class="flex items-center justify-between whitespace-nowrap px-10 py-4 backdrop-blur-sm bg-gray-900/50 sticky top-0 z-10">
<div class="flex items-center gap-4 text-cyan-400">
<span class="material-symbols-outlined text-3xl">
                hub
              </span>
<h2 class="text-xl font-bold tracking-[-0.015em]">Anas Alqahtani</h2>
</div>
<div class="flex flex-1 justify-end gap-8">
<div class="flex items-center gap-9 text-sm font-medium">
<a class="hover:text-cyan-400 transition-colors" href="#">About</a>
<a class="hover:text-cyan-400 transition-colors" href="#">Projects</a>
<a class="hover:text-cyan-400 transition-colors" href="#">Resume</a>
<a class="hover:text-cyan-400 transition-colors" href="#">Contact</a>
</div>
<div class="flex gap-4">
<a class="flex items-center justify-center rounded-full h-10 w-10 bg-gray-800/50 text-gray-400 hover:text-cyan-400 hover:bg-gray-700/50 transition-all duration-300" href="#">
<svg fill="currentColor" height="20px" viewBox="0 0 256 256" width="20px" xmlns="http://www.w3.org/2000/svg">
<path d="M208.31,75.68A59.78,59.78,0,0,0,202.93,28,8,8,0,0,0,196,24a59.75,59.75,0,0,0-48,24H124A59.75,59.75,0,0,0,76,24a8,8,0,0,0-6.93,4,59.78,59.78,0,0,0-5.38,47.68A58.14,58.14,0,0,0,56,104v8a56.06,56.06,0,0,0,48.44,55.47A39.8,39.8,0,0,0,96,192v8H72a24,24,0,0,1-24-24A40,40,0,0,0,8,136a8,8,0,0,0,0,16,24,24,0,0,1,24,24,40,40,0,0,0,40,40H96v16a8,8,0,0,0,16,0V192a24,24,0,0,1,48,0v40a8,8,0,0,0,16,0V192a39.8,39.8,0,0,0-8.44-24.53A56.06,56.06,0,0,0,216,112v-8A58.14,58.14,0,0,0,208.31,75.68ZM200,112a40,40,0,0,1-40,40H112a40,40,0,0,1-40-40v-8a41.74,41.74,0,0,1,6.9-22.48A8,8,0,0,0,80,73.83a43.81,43.81,0,0,1,.79-33.58,43.88,43.88,0,0,1,32.32,20.06A8,8,0,0,0,119.82,64h32.35a8,8,0,0,0,6.74-3.69,43.87,43.87,0,0,1,32.32-20.06A43.81,43.81,0,0,1,192,73.83a8.09,8.09,0,0,0,1,7.65A41.72,41.72,0,0,1,200,104Z"></path>
</svg>
</a>
<a class="flex items-center justify-center rounded-full h-10 w-10 bg-gray-800/50 text-gray-400 hover:text-cyan-400 hover:bg-gray-700/50 transition-all duration-300" href="#">
<svg fill="currentColor" height="20px" viewBox="0 0 256 256" width="20px" xmlns="http://www.w3.org/2000/svg">
<path d="M216,24H40A16,16,0,0,0,24,40V216a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V40A16,16,0,0,0,216,24Zm0,192H40V40H216V216ZM96,112v64a8,8,0,0,1-16,0V112a8,8,0,0,1,16,0Zm88,28v36a8,8,0,0,1-16,0V140a20,20,0,0,0-40,0v36a8,8,0,0,1-16,0V112a8,8,0,0,1,15.79-1.78A36,36,0,0,1,184,140ZM100,84A12,12,0,1,1,88,72,12,12,0,0,1,100,84Z"></path>
</svg>
</a>
</div>
</div>
</header>
<main class="px-20 lg:px-40 flex flex-1 justify-center py-10">
<div class="layout-content-container flex flex-col max-w-[960px] flex-1">
<div class="flex p-4 @container mb-12">
<div class="flex w-full flex-col gap-6 items-center text-center">
<div class="relative">
<div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full min-h-32 w-32" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuC7sVKzfqyrp7jUe1uokSS7BOfwyDF4QkRIvefPfiZ-0_anG0YRFObWJX5NtTgouRwrAHkAvCiUvKBxffqHC5KxIYIizBDkUHzEjs5hs505Ln797Mx6NG3TCyQbiDxGejVDV2DKbrbn4aayuoFzX4j4C9u55fFQ6E1ttIKz1xICbJJpXZMLbaj0D2hVgmpfXyTLHzI530inwZ39_xYwADJRtYW800Hx8v8Qdwoxg2W7xhattTWJ_kck8NsjkpehxVCBASOFQccx5WfG");'></div>
<div class="absolute inset-0 rounded-full border-2 border-cyan-400/50 animate-pulse"></div>
</div>
<div class="flex flex-col items-center justify-center gap-2">
<h1 class="text-3xl font-bold tracking-tight text-white">Anas Alqahtani</h1>
<p class="text-lg text-cyan-400">Information Systems Specialist</p>
<p class="text-gray-400 max-w-2xl mt-2">
                    Experienced IT professional specializing in system administration, network management, and cybersecurity. Passionate about leveraging technology to solve
                    complex business challenges.
                  </p>
</div>
</div>
</div>
<div class="relative">
<div class="absolute left-7 top-0 h-full w-0.5 bg-gradient-to-b from-cyan-500 via-purple-500 to-blue-500"></div>
<div class="grid grid-cols-[60px_1fr] gap-x-6 gap-y-8">
<div class="relative flex flex-col items-center gap-2">
<div class="flex items-center justify-center size-10 rounded-full bg-gray-800/80 backdrop-blur-sm border border-cyan-400/30 text-cyan-400 timeline-item-icon">
<span class="material-symbols-outlined text-2xl">school</span>
</div>
</div>
<div class="pt-1.5 backdrop-blur-sm bg-gray-900/30 rounded-lg p-4 border border-white/10 hover:border-cyan-400/50 transition-all duration-300">
<p class="text-lg font-semibold text-white">Master of Science in Information Systems</p>
<p class="text-gray-400">University of Technology, 2022</p>
</div>
<div class="relative flex flex-col items-center gap-2">
<div class="flex items-center justify-center size-10 rounded-full bg-gray-800/80 backdrop-blur-sm border border-cyan-400/30 text-cyan-400 timeline-item-icon">
<span class="material-symbols-outlined text-2xl">school</span>
</div>
</div>
<div class="pt-1.5 backdrop-blur-sm bg-gray-900/30 rounded-lg p-4 border border-white/10 hover:border-cyan-400/50 transition-all duration-300">
<p class="text-lg font-semibold text-white">Bachelor of Science in Computer Science</p>
<p class="text-gray-400">State University, 2020</p>
</div>
<div class="relative flex flex-col items-center gap-2">
<div class="flex items-center justify-center size-10 rounded-full bg-gray-800/80 backdrop-blur-sm border border-purple-500/30 text-purple-400 timeline-item-icon" style="animation-delay: 0.5s;">
<span class="material-symbols-outlined text-2xl">verified_user</span>
</div>
</div>
<div class="pt-1.5 backdrop-blur-sm bg-gray-900/30 rounded-lg p-4 border border-white/10 hover:border-purple-400/50 transition-all duration-300">
<p class="text-lg font-semibold text-white">Certified Information Systems Security Professional (CISSP)</p>
<p class="text-gray-400">Issued by (ISC)2, 2023</p>
</div>
<div class="relative flex flex-col items-center gap-2">
<div class="flex items-center justify-center size-10 rounded-full bg-gray-800/80 backdrop-blur-sm border border-purple-500/30 text-purple-400 timeline-item-icon" style="animation-delay: 1s;">
<span class="material-symbols-outlined text-2xl">security</span>
</div>
</div>
<div class="pt-1.5 backdrop-blur-sm bg-gray-900/30 rounded-lg p-4 border border-white/10 hover:border-purple-400/50 transition-all duration-300">
<p class="text-lg font-semibold text-white">CompTIA Security+</p>
<p class="text-gray-400">Issued by CompTIA, 2021</p>
</div>
<div class="relative flex flex-col items-center gap-2">
<div class="flex items-center justify-center size-10 rounded-full bg-gray-800/80 backdrop-blur-sm border border-blue-500/30 text-blue-400 timeline-item-icon" style="animation-delay: 1.5s;">
<span class="material-symbols-outlined text-2xl">router</span>
</div>
</div>
<div class="pt-1.5 backdrop-blur-sm bg-gray-900/30 rounded-lg p-4 border border-white/10 hover:border-blue-400/50 transition-all duration-300">
<p class="text-lg font-semibold text-white">Network+ Certification</p>
<p class="text-gray-400">Issued by CompTIA, 2021</p>
</div>
</div>
</div>
</div>
</main>
</div>
</div>

</body></html>