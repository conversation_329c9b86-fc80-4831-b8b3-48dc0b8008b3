<html><head>
<meta charset="utf-8"/>
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
<link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Roboto:wght@300;400;500;700&amp;family=Space+Grotesk:wght@300;400;500;600;700" onload="this.rel='stylesheet'" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style type="text/tailwindcss">
      :root {
        --dark-navy: #0a192f;
        --charcoal: #1d2d44;
        --neon-blue: #64ffda;
        --cyan: #8892b0;
        --purple: #a77df2;
      }
      body {
        font-family: 'Space Grotesk', sans-serif;
        background-color: var(--dark-navy);
        color: var(--cyan);
      }
      .form-input {
        background-color: rgba(29, 45, 68, 0.5);
        border: 1px solid var(--cyan);
        color: white;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
      }
      .form-input::placeholder {
        color: var(--cyan);
      }
      .form-input:focus {
        outline: none;
        border-color: var(--neon-blue);
        box-shadow: 0 0 0 2px rgba(100, 255, 218, 0.3);
      }
      .floating-label-group {
        position: relative;
      }
      .floating-label {
        position: absolute;
        top: 0.875rem;
        left: 0.75rem;
        color: var(--cyan);
        pointer-events: none;
        transition: all 0.2s ease-in-out;
      }
      .form-input:focus ~ .floating-label,
      .form-input:not(:placeholder-shown) ~ .floating-label {
        top: -0.625rem;
        left: 0.5rem;
        font-size: 0.75rem;
        background-color: var(--dark-navy);
        padding: 0 0.25rem;
        color: var(--neon-blue);
      }
      .social-icon {
        transition: all 0.3s ease;
      }
      .social-icon:hover {
        color: var(--neon-blue);
        transform: translateY(-3px);
      }
    </style>
</head>
<body class="bg-[var(--dark-navy)] text-[var(--cyan)]">
<div class="relative flex size-full min-h-screen flex-col overflow-x-hidden">
<div class="layout-container flex h-full grow flex-col">
<div class="flex flex-1 items-center justify-center py-10 md:py-20">
<div class="layout-content-container flex w-full max-w-4xl flex-col px-4">
<div class="mb-12 text-center">
<h2 class="text-4xl font-bold text-white md:text-5xl">Get In Touch</h2>
<p class="mt-4 text-lg">
                I'm currently looking for new opportunities. My inbox is always open. Whether you have a question or just want to say hi, I'll try my best to get back to you!
              </p>
</div>
<div class="w-full max-w-lg mx-auto">
<form class="space-y-8">
<div class="relative floating-label-group">
<input class="form-input peer h-14 w-full rounded-md border p-3" id="name" name="name" placeholder=" " type="text"/>
<label class="floating-label" for="name">Name</label>
</div>
<div class="relative floating-label-group">
<input class="form-input peer h-14 w-full rounded-md border p-3" id="email" name="email" placeholder=" " type="email"/>
<label class="floating-label" for="email">Email</label>
</div>
<div class="relative floating-label-group">
<textarea class="form-input peer min-h-36 w-full resize-y rounded-md border p-3" id="message" name="message" placeholder=" "></textarea>
<label class="floating-label" for="message">Message</label>
</div>
<div class="text-center">
<button class="group relative inline-flex h-12 items-center justify-center overflow-hidden rounded-md bg-[var(--neon-blue)] px-6 font-bold text-[var(--dark-navy)] transition-all duration-300 hover:bg-[var(--purple)]" type="submit">
<span class="relative">Send Message</span>
</button>
</div>
</form>
</div>
<div class="mt-16 flex justify-center space-x-8">
<a class="social-icon text-white" href="#">
<svg fill="currentColor" height="32" viewBox="0 0 256 256" width="32" xmlns="http://www.w3.org/2000/svg"><path d="M216,24H40A16,16,0,0,0,24,40V216a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V40A16,16,0,0,0,216,24Zm0,192H40V40H216V216ZM96,112v64a8,8,0,0,1-16,0V112a8,8,0,0,1,16,0Zm88,28v36a8,8,0,0,1-16,0V140a20,20,0,0,0-40,0v36a8,8,0,0,1-16,0V112a8,8,0,0,1,15.79-1.78A36,36,0,0,1,184,140ZM100,84A12,12,0,1,1,88,72,12,12,0,0,1,100,84Z"></path></svg>
</a>
<a class="social-icon text-white" href="#">
<svg fill="currentColor" height="32" viewBox="0 0 256 256" width="32" xmlns="http://www.w3.org/2000/svg"><path d="M208.31,75.68A59.78,59.78,0,0,0,202.93,28,8,8,0,0,0,196,24a59.75,59.75,0,0,0-48,24H124A59.75,59.75,0,0,0,76,24a8,8,0,0,0-6.93,4,59.78,59.78,0,0,0-5.38,47.68A58.14,58.14,0,0,0,56,104v8a56.06,56.06,0,0,0,48.44,55.47A39.8,39.8,0,0,0,96,192v8H72a24,24,0,0,1-24-24A40,40,0,0,0,8,136a8,8,0,0,0,0,16,24,24,0,0,1,24,24,40,40,0,0,0,40,40H96v16a8,8,0,0,0,16,0V192a24,24,0,0,1,48,0v40a8,8,0,0,0,16,0V192a39.8,39.8,0,0,0-8.44-24.53A56.06,56.06,0,0,0,216,112v-8A58.14,58.14,0,0,0,208.31,75.68ZM200,112a40,40,0,0,1-40,40H112a40,40,0,0,1-40-40v-8a41.74,41.74,0,0,1,6.9-22.48A8,8,0,0,0,80,73.83a43.81,43.81,0,0,1,.79-33.58,43.88,43.88,0,0,1,32.32,20.06A8,8,0,0,0,119.82,64h32.35a8,8,0,0,0,6.74-3.69,43.87,43.87,0,0,1,32.32-20.06A43.81,43.81,0,0,1,192,73.83a8.09,8.09,0,0,0,1,7.65A41.72,41.72,0,0,1,200,104Z"></path></svg>
</a>
<a class="social-icon text-white" href="#">
<svg fill="currentColor" height="32" viewBox="0 0 256 256" width="32" xmlns="http://www.w3.org/2000/svg"><path d="M224,48H32a8,8,0,0,0-8,8V192a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A8,8,0,0,0,224,48Zm-96,85.15L52.57,64H203.43ZM98.71,128,40,181.81V74.19Zm11.84,10.85,12,11.05a8,8,0,0,0,10.82,0l12-11.05,58,53.15H52.57ZM157.29,128,216,74.18V181.82Z"></path></svg>
</a>
</div>
</div>
</div>
<footer class="w-full py-6">
<div class="relative w-full h-px bg-gradient-to-r from-transparent via-[var(--neon-blue)] to-transparent"></div>
<div class="container mx-auto px-4 pt-6 text-center">
<p class="text-sm text-[var(--cyan)]">© 2024 Anas Alqahtani. All rights reserved.</p>
</div>
</footer>
</div>
</div>

</body></html>