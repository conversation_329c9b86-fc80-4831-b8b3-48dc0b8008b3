<html><head>
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
<link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Spline+Sans%3Awght%40400%3B500%3B700" onload="this.rel='stylesheet'" rel="stylesheet"/>
<title>Skills</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<meta charset="utf-8"/>
<link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet"/>
<style type="text/tailwindcss">
      :root {
        --primary-color: #00FFFF;
        --secondary-color: #9400D3;
      }
      .glow {
        box-shadow: 0 0 5px var(--primary-color), 0 0 10px var(--primary-color), 0 0 15px var(--primary-color);
      }
      .card-glow:hover {
        box-shadow: 0 0 10px var(--primary-color), 0 0 20px var(--primary-color), 0 0 30px var(--primary-color);
      }
      .card-glow:hover .material-symbols-outlined {
         color: var(--primary-color);
         text-shadow: 0 0 10px var(--primary-color);
      }
    </style>
</head>
<body class="bg-gray-900 text-white" style='font-family: "Spline Sans", "Noto Sans", sans-serif;'>
<div class="relative flex size-full min-h-screen flex-col group/design-root overflow-x-hidden">
<div class="layout-container flex h-full grow flex-col">
<header class="flex items-center justify-between whitespace-nowrap px-10 py-5 bg-gray-900/50 backdrop-blur-sm sticky top-0 z-50">
<div class="flex items-center gap-4 text-[var(--primary-color)]">
<span class="material-symbols-outlined text-3xl">
              query_stats
            </span>
<h2 class="text-xl font-bold leading-tight tracking-[-0.015em]">Portfolio</h2>
</div>
<div class="flex flex-1 justify-end gap-8">
<div class="flex items-center gap-9">
<a class="text-white hover:text-[var(--primary-color)] text-sm font-medium leading-normal transition-colors" href="#">About</a>
<a class="text-white hover:text-[var(--primary-color)] text-sm font-medium leading-normal transition-colors" href="#">Skills</a>
<a class="text-white hover:text-[var(--primary-color)] text-sm font-medium leading-normal transition-colors" href="#">Projects</a>
<a class="text-white hover:text-[var(--primary-color)] text-sm font-medium leading-normal transition-colors" href="#">Contact</a>
</div>
<div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10 border-2 border-[var(--primary-color)] glow" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuChwR-V7i6OoIPduDTKY6gSOlBdCtLOCuLl6550DVpaKJhS8rlG_AmQ2o_9V5zCX0gjdJLSEwyaQjULdUatk7KyJ0w1eQqI34tHH1K_pIgHjQgCx9sJJYxlr7VM4liOws-MXameqaA5B17i-IIwpXkcAhQWM3Jp1mIUPcNtHGQkUaCnqdEBGtjMFrnO5AlqBEP2WcLQtUTG2Z_NIf-P7_LRaxyC52QyOtRXXUUYYKNRZukkP9SGVzG4fIJE3ekELTSnA6ck9BDrmd6z");'></div>
</div>
</header>
<div class="px-40 flex flex-1 justify-center py-5">
<div class="layout-content-container flex flex-col max-w-[960px] flex-1">
<div class="@container">
<div class="@[480px]:p-4">
<div class="flex min-h-[480px] flex-col gap-6 bg-cover bg-center bg-no-repeat @[480px]:gap-8 @[480px]:rounded-lg items-center justify-center p-4 bg-gray-900/50 backdrop-blur-md border border-gray-700" style="background-image: linear-gradient(rgba(10, 10, 20, 0.7) 0%, rgba(10, 10, 20, 0.9) 100%);">
<div class="flex flex-col gap-4 text-center">
<h1 class="text-white text-4xl font-black leading-tight tracking-[-0.033em] @[480px]:text-6xl @[480px]:font-black @[480px]:leading-tight @[480px]:tracking-[-0.033em] text-shadow-lg" style="text-shadow: 0 0 10px var(--primary-color), 0 0 20px var(--secondary-color);">Information Systems Specialist</h1>
<p class="text-gray-300 text-base font-normal leading-normal @[480px]:text-lg @[480px]:font-normal @[480px]:leading-normal max-w-3xl">
                      I am an Information Systems Specialist with a passion for technology and innovation. I specialize in designing, implementing, and managing information systems to meet organizational needs and drive business success.
                    </p>
</div>
<button class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-12 px-5 bg-[var(--primary-color)] text-gray-900 text-base font-bold leading-normal tracking-[0.015em] transition-all hover:bg-white hover:shadow-[0_0_20px_var(--primary-color)]">
<span class="truncate">View Projects</span>
</button>
</div>
</div>
</div>
<h2 class="text-white text-[28px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-10 text-center">Key Skills</h2>
<div class="grid grid-cols-[repeat(auto-fit,minmax(250px,1fr))] gap-8 p-4">
<div class="flex flex-col gap-4 p-6 rounded-lg bg-gray-900/50 backdrop-blur-md border border-gray-700 transition-all card-glow">
<span class="material-symbols-outlined text-5xl text-gray-400 transition-colors">database</span>
<div>
<p class="text-white text-xl font-bold leading-normal">Databases</p>
<p class="text-gray-400 text-sm font-normal leading-normal">Design, implement, and manage databases to ensure data integrity and availability.</p>
</div>
</div>
<div class="flex flex-col gap-4 p-6 rounded-lg bg-gray-900/50 backdrop-blur-md border border-gray-700 transition-all card-glow">
<span class="material-symbols-outlined text-5xl text-gray-400 transition-colors">security</span>
<div>
<p class="text-white text-xl font-bold leading-normal">Cybersecurity</p>
<p class="text-gray-400 text-sm font-normal leading-normal">Protect systems and data from unauthorized access and cyber threats.</p>
</div>
</div>
<div class="flex flex-col gap-4 p-6 rounded-lg bg-gray-900/50 backdrop-blur-md border border-gray-700 transition-all card-glow">
<span class="material-symbols-outlined text-5xl text-gray-400 transition-colors">cloud</span>
<div>
<p class="text-white text-xl font-bold leading-normal">Cloud Computing</p>
<p class="text-gray-400 text-sm font-normal leading-normal">Architect and manage cloud-based solutions for scalability and efficiency.</p>
</div>
</div>
<div class="flex flex-col gap-4 p-6 rounded-lg bg-gray-900/50 backdrop-blur-md border border-gray-700 transition-all card-glow">
<span class="material-symbols-outlined text-5xl text-gray-400 transition-colors">hub</span>
<div>
<p class="text-white text-xl font-bold leading-normal">Networking</p>
<p class="text-gray-400 text-sm font-normal leading-normal">Configure and maintain network infrastructure for seamless connectivity.</p>
</div>
</div>
<div class="flex flex-col gap-4 p-6 rounded-lg bg-gray-900/50 backdrop-blur-md border border-gray-700 transition-all card-glow">
<span class="material-symbols-outlined text-5xl text-gray-400 transition-colors">analytics</span>
<div>
<p class="text-white text-xl font-bold leading-normal">System Analysis</p>
<p class="text-gray-400 text-sm font-normal leading-normal">Analyze system requirements and design solutions to optimize performance.</p>
</div>
</div>
<div class="flex flex-col gap-4 p-6 rounded-lg bg-gray-900/50 backdrop-blur-md border border-gray-700 transition-all card-glow">
<span class="material-symbols-outlined text-5xl text-gray-400 transition-colors">manage_accounts</span>
<div>
<p class="text-white text-xl font-bold leading-normal">IT Project Management</p>
<p class="text-gray-400 text-sm font-normal leading-normal">Manage IT projects from initiation to completion, ensuring timely delivery.</p>
</div>
</div>
</div>
</div>
</div>
<footer class="flex justify-center">
<div class="flex max-w-[960px] flex-1 flex-col">
<footer class="flex flex-col gap-6 px-5 py-10 text-center @container border-t border-gray-800">
<div class="flex flex-wrap justify-center gap-6">
<a class="text-gray-400 hover:text-[var(--primary-color)] transition-colors" href="#">
<svg fill="currentColor" height="28px" viewBox="0 0 256 256" width="28px" xmlns="http://www.w3.org/2000/svg">
<path d="M247.39,68.94A8,8,0,0,0,240,64H209.57A48.66,48.66,0,0,0,168.1,40a46.91,46.91,0,0,0-33.75,13.7A47.9,47.9,0,0,0,120,88v6.09C79.74,83.47,46.81,50.72,46.46,50.37a8,8,0,0,0-13.65,4.92c-4.31,47.79,9.57,79.77,22,98.18a110.93,110.93,0,0,0,21.88,24.2c-15.23,17.53-39.21,26.74-39.47,26.84a8,8,0,0,0-3.85,11.93c.75,1.12,3.75,5.05,11.08,8.72C53.51,229.7,65.48,232,80,232c70.67,0,129.72-54.42,135.75-124.44l29.91-29.9A8,8,0,0,0,247.39,68.94Zm-45,29.41a8,8,0,0,0-2.32,5.14C196,166.58,143.28,216,80,216c-10.56,0-18-1.4-23.22-3.08,11.51-6.25,27.56-17,37.88-32.48A8,8,0,0,0,92,169.08c-.47-.27-43.91-26.34-44-96,16,13,45.25,33.17,78.67,38.79A8,8,0,0,0,136,104V88a32,32,0,0,1,9.6-22.92A30.94,30.94,0,0,1,167.9,56c12.66.16,24.49,7.88,29.44,19.21A8,8,0,0,0,204.67,80h16Z"></path>
</svg>
</a>
<a class="text-gray-400 hover:text-[var(--primary-color)] transition-colors" href="#">
<svg fill="currentColor" height="28px" viewBox="0 0 256 256" width="28px" xmlns="http://www.w3.org/2000/svg">
<path d="M216,24H40A16,16,0,0,0,24,40V216a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V40A16,16,0,0,0,216,24Zm0,192H40V40H216V216ZM96,112v64a8,8,0,0,1-16,0V112a8,8,0,0,1,16,0Zm88,28v36a8,8,0,0,1-16,0V140a20,20,0,0,0-40,0v36a8,8,0,0,1-16,0V112a8,8,0,0,1,15.79-1.78A36,36,0,0,1,184,140ZM100,84A12,12,0,1,1,88,72,12,12,0,0,1,100,84Z"></path>
</svg>
</a>
<a class="text-gray-400 hover:text-[var(--primary-color)] transition-colors" href="#">
<svg fill="currentColor" height="28px" viewBox="0 0 256 256" width="28px" xmlns="http://www.w3.org/2000/svg">
<path d="M208.31,75.68A59.78,59.78,0,0,0,202.93,28,8,8,0,0,0,196,24a59.75,59.75,0,0,0-48,24H124A59.75,59.75,0,0,0,76,24a8,8,0,0,0-6.93,4,59.78,59.78,0,0,0-5.38,47.68A58.14,58.14,0,0,0,56,104v8a56.06,56.06,0,0,0,48.44,55.47A39.8,39.8,0,0,0,96,192v8H72a24,24,0,0,1-24-24A40,40,0,0,0,8,136a8,8,0,0,0,0,16,24,24,0,0,1,24,24,40,40,0,0,0,40,40H96v16a8,8,0,0,0,16,0V192a24,24,0,0,1,48,0v40a8,8,0,0,0,16,0V192a39.8,39.8,0,0,0-8.44-24.53A56.06,56.06,0,0,0,216,112v-8A58.14,58.14,0,0,0,208.31,75.68ZM200,112a40,40,0,0,1-40,40H112a40,40,0,0,1-40-40v-8a41.74,41.74,0,0,1,6.9-22.48A8,8,0,0,0,80,73.83a43.81,43.81,0,0,1,.79-33.58,43.88,43.88,0,0,1,32.32,20.06A8,8,0,0,0,119.82,64h32.35a8,8,0,0,0,6.74-3.69,43.87,43.87,0,0,1,32.32-20.06A43.81,43.81,0,0,1,192,73.83a8.09,8.09,0,0,0,1,7.65A41.72,41.72,0,0,1,200,104Z"></path>
</svg>
</a>
</div>
<p class="text-gray-500 text-base font-normal leading-normal">@2024 Anas Alqahtani. All rights reserved.</p>
</footer>
</div>
</footer>
</div>
</div>

</body></html>