<html><head>
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
<link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Inter:wght@400;500;700;900&amp;family=Syne:wght@400;500;700" onload="this.rel='stylesheet'" rel="stylesheet"/>
<title>Project</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
<meta charset="utf-8"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style type="text/tailwindcss">
      :root {
        --c-blue: #00bfff;
        --c-cyan: #00ffff;
        --c-purple: #8a2be2;
      }
      .glassmorphism {
        background: rgba(10, 10, 20, 0.6);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
      }
      .glow-border {
        position: relative;
        overflow: hidden;
      }
      .glow-border::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: conic-gradient(
          transparent,
          var(--c-cyan),
          var(--c-blue),
          var(--c-purple),
          transparent
        );
        animation: rotate 6s linear infinite;
        z-index: -1;
      }
      @keyframes rotate {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }
    </style>
</head>
<body class="bg-[#0A0A14] text-white" style='font-family: "Inter", "Syne", sans-serif;'>
<div class="relative flex size-full min-h-screen flex-col group/design-root overflow-x-hidden">
<div class="layout-container flex h-full grow flex-col">
<header class="flex items-center justify-between whitespace-nowrap px-10 py-5 z-10 glassmorphism border-b border-b-[rgba(255,255,255,0.1)]">
<div class="flex items-center gap-4 text-[var(--c-cyan)]">
<div class="size-6">
<svg fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_6_330)">
<path clip-rule="evenodd" d="M24 0.757355L47.2426 24L24 47.2426L0.757355 24L24 0.757355ZM21 35.7574V12.2426L9.24264 24L21 35.7574Z" fill="currentColor" fill-rule="evenodd"></path>
</g>
<defs>
<clipPath id="clip0_6_330"><rect fill="white" height="48" width="48"></rect></clipPath>
</defs>
</svg>
</div>
<h2 class="text-xl font-bold leading-tight tracking-[-0.015em]" style="font-family: 'Syne', sans-serif;">Anas Alqahtani</h2>
</div>
<nav class="flex flex-1 justify-center gap-8 text-sm font-medium">
<a class="hover:text-[var(--c-cyan)] transition-colors" href="#">About</a>
<a class="text-[var(--c-cyan)]" href="#">Projects</a>
<a class="hover:text-[var(--c-cyan)] transition-colors" href="#">Contact</a>
</nav>
<div class="flex items-center gap-3">
<button class="flex items-center justify-center rounded-full h-10 w-10 bg-[rgba(255,255,255,0.1)] hover:bg-[var(--c-cyan)] hover:text-[#0A0A14] transition-colors duration-300">
<svg fill="currentColor" height="20px" viewBox="0 0 256 256" width="20px" xmlns="http://www.w3.org/2000/svg">
<path d="M208.31,75.68A59.78,59.78,0,0,0,202.93,28,8,8,0,0,0,196,24a59.75,59.75,0,0,0-48,24H124A59.75,59.75,0,0,0,76,24a8,8,0,0,0-6.93,4,59.78,59.78,0,0,0-5.38,47.68A58.14,58.14,0,0,0,56,104v8a56.06,56.06,0,0,0,48.44,55.47A39.8,39.8,0,0,0,96,192v8H72a24,24,0,0,1-24-24A40,40,0,0,0,8,136a8,8,0,0,0,0,16,24,24,0,0,1,24,24,40,40,0,0,0,40,40H96v16a8,8,0,0,0,16,0V192a24,24,0,0,1,48,0v40a8,8,0,0,0,16,0V192a39.8,39.8,0,0,0-8.44-24.53A56.06,56.06,0,0,0,216,112v-8A58.14,58.14,0,0,0,208.31,75.68ZM200,112a40,40,0,0,1-40,40H112a40,40,0,0,1-40-40v-8a41.74,41.74,0,0,1,6.9-22.48A8,8,0,0,0,80,73.83a43.81,43.81,0,0,1,.79-33.58,43.88,43.88,0,0,1,32.32,20.06A8,8,0,0,0,119.82,64h32.35a8,8,0,0,0,6.74-3.69,43.87,43.87,0,0,1,32.32-20.06A43.81,43.81,0,0,1,192,73.83a8.09,8.09,0,0,0,1,7.65A41.72,41.72,0,0,1,200,104Z"></path>
</svg>
</button>
<button class="flex items-center justify-center rounded-full h-10 w-10 bg-[rgba(255,255,255,0.1)] hover:bg-[var(--c-cyan)] hover:text-[#0A0A14] transition-colors duration-300">
<svg fill="currentColor" height="20px" viewBox="0 0 256 256" width="20px" xmlns="http://www.w3.org/2000/svg">
<path d="M216,24H40A16,16,0,0,0,24,40V216a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V40A16,16,0,0,0,216,24Zm0,192H40V40H216V216ZM96,112v64a8,8,0,0,1-16,0V112a8,8,0,0,1,16,0Zm88,28v36a8,8,0,0,1-16,0V140a20,20,0,0,0-40,0v36a8,8,0,0,1-16,0V112a8,8,0,0,1,15.79-1.78A36,36,0,0,1,184,140ZM100,84A12,12,0,1,1,88,72,12,12,0,0,1,100,84Z"></path>
</svg>
</button>
<div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10 border-2 border-[var(--c-cyan)]" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuB6-0Io27VZ1lcogUMbp9q8oRxJyrB4UMlGWEHdrdqXCdxmxwEQAkEDFNRscwIdreL-X8nqeTSFKGDj5CAHlfG47YMhy5BQ2GkRhthO-raJcZ0aHgw0yyr2hMFYv_uJxuR8sfp1VxzbqWIrBdbpqlBQi3oo_3MM75e1mE_VMhbqUKgeJhsdhcM36r7TlKPZgL_fawcmdJM2vCcbMxRwAw0lK3DPl6Y1qRUe8RNIdQ6HU0LG6DDV4Pe3lhE1lxLLPbNo9A4jH5_439nI");'></div>
</div>
</header>
<main class="px-10 md:px-20 lg:px-40 flex flex-1 justify-center py-10">
<div class="layout-content-container flex flex-col max-w-5xl flex-1">
<section class="flex flex-col items-center justify-center text-center py-20">
<h1 class="text-5xl md:text-7xl font-black leading-tight tracking-tighter mb-4" style="font-family: 'Syne', sans-serif;">
<span class="bg-clip-text text-transparent bg-gradient-to-r from-[var(--c-blue)] to-[var(--c-cyan)]">
                  Anas Alqahtani
                </span>
</h1>
<h2 class="text-xl md:text-2xl text-slate-300 mb-8">Information Systems Specialist</h2>
<button class="relative overflow-hidden cursor-pointer rounded-lg h-12 px-6 bg-gradient-to-r from-[var(--c-blue)] to-[var(--c-cyan)] text-black text-base font-bold transition-all duration-300 hover:shadow-[0_0_20px_var(--c-cyan)]">
<span class="truncate">View My Work</span>
</button>
</section>
<h2 class="text-3xl font-bold tracking-tight pb-6 pt-12 text-center" style="font-family: 'Syne', sans-serif;">Featured Projects</h2>
<div class="grid grid-cols-1 md:grid-cols-2 gap-8">
<div class="group relative overflow-hidden rounded-lg glow-border transition-all duration-500 hover:scale-105">
<div class="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBh15V-Lq4p-yat5ghSigePVA2heJqQODh71aMJSGDtQjdSW4UmeCVnIMPoEmjyED0OJcQURXmuQNrYNrGjbmzcuNxezquBvgbKwCHYBZz6hYdBZYnVUiKEpWvG4EaIMq-1sHOSDuqG1xGeOD2ulDcAPqa4sKzhAoJr_QpGGxQ5BesVND_3eEudzGXVLyrxEDx4Q_oAH7w4wRdn1691qZihSjgGeoH46OKRdq1b2Qx2MlAGfUwQl_EwPC6yfOc1mn3Ffc1EnuE2hSYs");'></div>
<div class="absolute inset-0 bg-black bg-opacity-60 group-hover:bg-opacity-80 transition-all duration-500"></div>
<div class="relative p-6 flex flex-col justify-end h-80">
<h3 class="text-xl font-bold leading-tight mb-2 text-white">Data Analytics Dashboard</h3>
<p class="text-slate-300 text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-500">A comprehensive dashboard for visualizing and analyzing complex datasets, providing actionable insights.</p>
</div>
</div>
<div class="group relative overflow-hidden rounded-lg glow-border transition-all duration-500 hover:scale-105">
<div class="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDu0aOXUqHm86qJ0h5144qho1pEuuvb64jqZnnIqX3iAqausTwJyIjIxCd6gbSlVANTgD50p3iymNsHOK152EjCZqZOXI84HPouLVTXG_YP1HHD_GVANbG1BozGHJtn5rb0qWgEIpU8uQO994T_6l7S7RBv5FIx7b6qWQ4rLkGj8mZ5oUhCJizwbzFaFcOC5RXHa55ri1N80VKjrwsidW_U4-WjCdfY6STov3ah50wwAhm7V8GS5oBcAigLk7rX-aWoBdRK1GqtFz0t");'></div>
<div class="absolute inset-0 bg-black bg-opacity-60 group-hover:bg-opacity-80 transition-all duration-500"></div>
<div class="relative p-6 flex flex-col justify-end h-80">
<h3 class="text-xl font-bold leading-tight mb-2 text-white">E-commerce Platform</h3>
<p class="text-slate-300 text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-500">A scalable and secure e-commerce solution with a custom CMS and payment gateway integration.</p>
</div>
</div>
<div class="group relative overflow-hidden rounded-lg glow-border transition-all duration-500 hover:scale-105">
<div class="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDkdmR0vkptfPUzhmuNrhAhebwQjvBRIEbFzTlxfkWxyxIt8b4A5FkrSnl1pCUEdtzfsD10O1zSSWkP4Uor3rPo08RodXfTcvyW8iCjeQjPMzBxjhrVy-RY0hiNYaz_h1EAgSrKFiBTTFElkuUyZb0jx6h1V5uggmiav2jFMgmCWBkF_lwFLVDamG-AZcC_xLW0lFGf4l4TjZML1gGJGtg4ldOITSKcnT17lqFKQYYVurWNcq9OVJghMYWguHWrBmmq6o5C5-CC3Vc_");'></div>
<div class="absolute inset-0 bg-black bg-opacity-60 group-hover:bg-opacity-80 transition-all duration-500"></div>
<div class="relative p-6 flex flex-col justify-end h-80">
<h3 class="text-xl font-bold leading-tight mb-2 text-white">Mobile App for Fitness Tracking</h3>
<p class="text-slate-300 text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-500">A native mobile app for iOS and Android to track workouts, nutrition, and progress.</p>
</div>
</div>
<div class="group relative overflow-hidden rounded-lg glow-border transition-all duration-500 hover:scale-105">
<div class="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuAniJOyyIprzOKmDEiaNfU6WRtq9b22Lp54601l8I7ctRgDekduhiHwKu7NnnyVP6f2At_Zh041MRAJW-wbww1qfiCQUnPniTS2N9ywaFl42CoRGoqOMHSxXDnjd-OsSwSPEXMF-w122LYX2mT7S9PPCbHqHTHSfO33ezsHlgIz5tk-PwO-rU1VtLgBABU3TOuho_-Jpffx5PET0CsVQB3JKfaDP9YLnctunR_1D1pzzZy317Tv40SzICeAdcuRi6riiBmboDv6DnKd");'></div>
<div class="absolute inset-0 bg-black bg-opacity-60 group-hover:bg-opacity-80 transition-all duration-500"></div>
<div class="relative p-6 flex flex-col justify-end h-80">
<h3 class="text-xl font-bold leading-tight mb-2 text-white">Cloud Infrastructure Management</h3>
<p class="text-slate-300 text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-500">Automated deployment and management of cloud resources for high availability and scalability.</p>
</div>
</div>
</div>
<section class="mt-20 py-10 glassmorphism rounded-lg p-8">
<h2 class="text-3xl font-bold tracking-tight pb-6 text-center" style="font-family: 'Syne', sans-serif;">About Me</h2>
<p class="text-slate-300 text-base leading-relaxed text-center max-w-3xl mx-auto">
                I am an Information Systems Specialist with over 5 years of experience in designing, implementing, and managing IT solutions. My expertise includes data analytics,
                cloud computing, and software development. I am passionate about leveraging technology to solve complex business challenges and drive innovation.
              </p>
</section>
<section class="mt-12 mb-20 text-center">
<h2 class="text-3xl font-bold tracking-tight pb-4" style="font-family: 'Syne', sans-serif;">Get In Touch</h2>
<p class="text-slate-300 text-base leading-relaxed mb-6 max-w-2xl mx-auto">
                Feel free to reach out to me for collaborations or new opportunities. I'm always open to discussing new projects and ideas.
              </p>
<a class="text-[var(--c-cyan)] font-semibold hover:underline" href="mailto:<EMAIL>"><EMAIL></a>
</section>
</div>
</main>
</div>
</div>

</body></html>